"""
Enhanced Agent Example - Shows how to extend the ConfigurableAgent
with conversation completion detection and automatic output generation
"""

from configurable_agent import Configurable<PERSON>gent, UserStoryData
from livekit.agents import AgentSession
import re
from typing import Optional

class EnhancedStoryAgent(ConfigurableAgent):
    """
    Extended version with automatic conversation completion detection
    Demonstrates how to extend the base agent while keeping it simple
    """
    
    def __init__(self, context_file_path: str, instructions_file_path: Optional[str] = None):
        super().__init__(context_file_path, instructions_file_path)
        self.conversation_turns = 0
        self.completion_keywords = [
            "that's all", "we're done", "looks good", "that covers it",
            "nothing else", "complete", "finished", "ready to finalize"
        ]
    
    async def process_user_input(self, user_message: str):
        """
        Process user input and extract story information
        This is a simple example - in practice, you'd use more sophisticated NLP
        """
        self.conversation_turns += 1
        
        # Simple keyword extraction (extend this with proper NLP)
        if "as a" in user_message.lower():
            # Extract user persona
            persona_match = re.search(r"as a ([^,]+)", user_message.lower())
            if persona_match:
                self.update_story_data(user_persona=persona_match.group(1).strip())
        
        if "i want" in user_message.lower():
            # Extract functionality
            func_match = re.search(r"i want (?:to )?([^,]+)", user_message.lower())
            if func_match:
                self.update_story_data(functionality=func_match.group(1).strip())
        
        if "so that" in user_message.lower():
            # Extract business value
            value_match = re.search(r"so that ([^.]+)", user_message.lower())
            if value_match:
                self.update_story_data(business_value=value_match.group(1).strip())
        
        # Check for completion signals
        if self.should_complete_conversation(user_message):
            await self.generate_final_output()
            return True
        
        return False
    
    def should_complete_conversation(self, user_message: str) -> bool:
        """
        Simple completion detection logic
        Extend this with more sophisticated detection
        """
        message_lower = user_message.lower()
        
        # Check for completion keywords
        if any(keyword in message_lower for keyword in self.completion_keywords):
            return True
        
        # Check if we have minimum required info and user seems satisfied
        if (self.story_data.is_complete() and 
            self.conversation_turns >= 5 and
            ("yes" in message_lower or "correct" in message_lower)):
            return True
        
        return False
    
    def get_conversation_progress(self) -> dict:
        """Get current progress for debugging/monitoring"""
        return {
            "turns": self.conversation_turns,
            "phase": self.conversation_phase,
            "completeness": self.story_data.is_complete(),
            "has_persona": bool(self.story_data.user_persona),
            "has_functionality": bool(self.story_data.functionality),
            "has_business_value": bool(self.story_data.business_value),
            "criteria_count": len(self.story_data.acceptance_criteria),
            "dependencies_count": len(self.story_data.dependencies)
        }

# Example usage function
async def demo_enhanced_agent():
    """Demo the enhanced agent functionality"""
    
    # Create enhanced agent
    agent = EnhancedStoryAgent("agent_context.txt", "agent_instructions.txt")
    
    # Simulate conversation inputs
    test_inputs = [
        "Hi, I want to create a user story",
        "As a store owner, I want to quickly reorder products",
        "So that I can save time and avoid stockouts",
        "This would be used during busy hours",
        "When inventory is running low, the system should suggest reorders",
        "That covers everything, looks good to me"
    ]
    
    print("Enhanced Agent Demo - Conversation Simulation")
    print("="*50)
    
    for i, user_input in enumerate(test_inputs, 1):
        print(f"\nTurn {i}: User says: '{user_input}'")
        
        # Process the input
        completed = await agent.process_user_input(user_input)
        
        # Show progress
        progress = agent.get_conversation_progress()
        print(f"Progress: {progress}")
        
        if completed:
            print("✅ Conversation completed and final output generated!")
            break
    
    return agent

if __name__ == "__main__":
    import asyncio
    
    print("This example shows how to extend the base ConfigurableAgent")
    print("with automatic conversation completion detection.\n")
    
    # Note: This is just a demo - actual implementation would need proper async context
    print("Run this within a proper LiveKit agent context for full functionality.")
    print("The key concepts demonstrated:")
    print("1. Extending the base agent class")
    print("2. Adding conversation state tracking") 
    print("3. Implementing completion detection")
    print("4. Automatic final output generation")
