#!/usr/bin/env python3
"""
Test Step 2: Function Tools
This script tests the function tools implementation for structured data collection
"""

import asyncio
from configurable_agent import ConfigurableAgent, UserStoryData, ConversationPhase, RunContext

async def test_function_tools():
    """Test function tools for structured data collection"""
    
    print("="*60)
    print("TESTING STEP 2: FUNCTION TOOLS")
    print("="*60)
    
    # Create agent
    agent = ConfigurableAgent("agent_context.txt", "agent_instructions.txt")
    context = RunContext()
    
    print(f"✅ Agent created with tools")
    print(f"✅ Initial phase: {agent.conversation_phase.value}")
    
    print("\n--- Testing Collect Persona Tool ---")
    response_agent, response_message = await agent.collect_persona(
        context,
        persona="small retail store owner",
        pain_points="inventory management and cashflow",
        goals="maximize profits and minimize stockouts"
    )
    print(f"Tool response: {response_message}")
    print(f"Updated persona: {agent.story_data.user_persona}")
    print(f"Current phase: {agent.conversation_phase.value}")
    
    print("\n--- Testing Collect Functionality Tool ---")
    _, response_message = await agent.collect_functionality(
        context,
        functionality="quickly reorder popular products with one tap"
    )
    print(f"Tool response: {response_message}")
    print(f"Updated functionality: {agent.story_data.functionality}")
    print(f"Current phase: {agent.conversation_phase.value}")
    
    print("\n--- Testing Collect Business Value Tool ---")
    _, response_message = await agent.collect_business_value(
        context,
        business_value="maintain optimal inventory levels without spending time on complex ordering"
    )
    print(f"Tool response: {response_message}")
    print(f"Updated value: {agent.story_data.business_value}")
    print(f"Current phase: {agent.conversation_phase.value}")
    
    print("\n--- Testing Collect Context Tool ---")
    _, response_message = await agent.collect_context(
        context,
        usage_context="during busy store hours when noticing popular products running low"
    )
    print(f"Tool response: {response_message}")
    print(f"Updated context: {agent.story_data.context}")
    print(f"Current phase: {agent.conversation_phase.value}")
    
    print("\n--- Testing Collect Acceptance Criteria Tool ---")
    _, response_message = await agent.collect_acceptance_criteria(
        context,
        criteria="Given I'm logged in, When I tap 'Quick Reorder', Then I see my top 10 best-selling products"
    )
    print(f"Tool response: {response_message}")
    print(f"Acceptance criteria count: {len(agent.story_data.acceptance_criteria)}")
    
    print("\n--- Testing Collect Dependencies Tool ---")
    _, response_message = await agent.collect_dependencies(
        context,
        dependency="Integration with inventory management system"
    )
    print(f"Tool response: {response_message}")
    print(f"Dependencies count: {len(agent.story_data.dependencies)}")
    
    print("\n--- Testing Finalize Story Tool ---")
    try:
        # This will fail because we don't have a session set
        _, response_message = await agent.finalize_user_story(context)
        print(f"Tool response: {response_message}")
    except Exception as e:
        print(f"Expected error (no session): {e}")
    
    print("\n✅ STEP 2 VERIFICATION COMPLETE")
    print("✅ Function tools implemented successfully")
    
    print("\nFinal Story Data:")
    print(f"- Persona: {agent.story_data.user_persona}")
    print(f"- Functionality: {agent.story_data.functionality}")
    print(f"- Business Value: {agent.story_data.business_value}")
    print(f"- Context: {agent.story_data.context}")
    print(f"- Acceptance Criteria: {agent.story_data.acceptance_criteria}")
    print(f"- Dependencies: {agent.story_data.dependencies}")
    print(f"- Completion: {agent.story_data.get_completion_percentage():.1f}%")
    print(f"- Is Complete: {agent.story_data.is_complete()}")
    
    return agent

if __name__ == "__main__":
    print("Step 2 Verification: Function Tools for Structured Data Collection")
    print("This tests the enhanced agent with function tools\n")
    
    try:
        # Run the test asynchronously
        agent = asyncio.run(test_function_tools())
        
        # Print formatted story
        print("\n--- GENERATED USER STORY ---")
        print(agent.story_data.to_formatted_story())
        
        print("\n🎉 Step 2 implementation verified successfully!")
        print("Next: Implement Advanced Conversation Management (Step 3)")
    except Exception as e:
        print(f"Error testing function tools: {e}")
