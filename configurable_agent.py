from dotenv import load_dotenv
import os
from typing import Optional, List
from dataclasses import dataclass
from enum import Enum

from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions, RunContext, function_tool
from livekit.plugins import (
    openai,
    cartesia,
    deepgram,
    noise_cancellation,
    silero,
)
from livekit.plugins.turn_detector.multilingual import MultilingualModel

load_dotenv()


class ConversationPhase(Enum):
    """Enum to track conversation phases"""
    GREETING = "greeting"
    COLLECTING_PERSONA = "collecting_persona"
    COLLECTING_FUNCTIONALITY = "collecting_functionality"
    COLLECTING_VALUE = "collecting_value"
    COLLECTING_CONTEXT = "collecting_context"
    COLLECTING_CRITERIA = "collecting_criteria"
    FINALIZING = "finalizing"
    COMPLETE = "complete"


@dataclass
class UserStoryData:
    """Simple data structure to track user story information"""
    user_persona: str = ""
    functionality: str = ""
    business_value: str = ""
    context: str = ""
    acceptance_criteria: List[str] = None
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.acceptance_criteria is None:
            self.acceptance_criteria = []
        if self.dependencies is None:
            self.dependencies = []
    
    def is_complete(self) -> bool:
        """Check if we have minimum required information for a user story"""
        return bool(self.user_persona and self.functionality and self.business_value)
    
    def get_completion_percentage(self) -> float:
        """Calculate how complete the user story is"""
        required_fields = ['user_persona', 'functionality', 'business_value']
        completed = sum(1 for field in required_fields if getattr(self, field))
        return (completed / len(required_fields)) * 100
    
    def to_formatted_story(self) -> str:
        """Generate the final formatted user story"""
        story = f"As a {self.user_persona}, I want to {self.functionality} so that {self.business_value}."
        
        if self.context:
            story += f"\n\nContext: {self.context}"
        
        if self.acceptance_criteria:
            story += "\n\nAcceptance Criteria:"
            for i, criteria in enumerate(self.acceptance_criteria, 1):
                story += f"\n{i}. {criteria}"
        
        if self.dependencies:
            story += "\n\nDependencies:"
            for dep in self.dependencies:
                story += f"\n- {dep}"
        
        return story


class ConfigurableAgent(Agent):
    """
    A configurable agent that can load context from a file and instructions for gathering
    information from another file.
    """
    def __init__(self, context_file_path: str, instructions_file_path: Optional[str] = None) -> None:
        # Load context from the context file
        try:
            # Fix 5: Add encoding specification
            with open(context_file_path, 'r', encoding='utf-8') as f:
                context = f.read().strip()
        except FileNotFoundError:
            print(f"Warning: Context file {context_file_path} not found. Using default context.")
            context = "You are a helpful voice AI assistant."
        except UnicodeDecodeError:
            print(f"Warning: Context file {context_file_path} has encoding issues. Using default context.")
            context = "You are a helpful voice AI assistant."

        # Load instructions from the instructions file if provided
        instructions = None
        if instructions_file_path:
            try:
                # Fix 5: Add encoding specification
                with open(instructions_file_path, 'r', encoding='utf-8') as f:
                    instructions = f.read().strip()
            except FileNotFoundError:
                print(f"Warning: Instructions file {instructions_file_path} not found.")
            except UnicodeDecodeError:
                print(f"Warning: Instructions file {instructions_file_path} has encoding issues.")
        
        # Initialize the agent with the loaded context and function tools
        tools = [
            self.collect_persona, 
            self.collect_functionality,
            self.collect_business_value, 
            self.collect_context,
            self.collect_acceptance_criteria, 
            self.collect_dependencies,
            self.finalize_user_story
        ]
        
        super().__init__(instructions=context, tools=tools)
        
        # Store instructions and initialize conversation state
        self.user_instructions = instructions
        self.story_data = UserStoryData()
        self.conversation_phase = ConversationPhase.GREETING
        self.session = None
        self.conversation_turns = 0
    
    async def on_enter(self):
        """Called when agent becomes active - start the conversation"""
        print(f"Agent entering conversation in phase: {self.conversation_phase.value}")

        # Fix: According to latest LiveKit docs, on_enter should not call generate_reply
        # The session will handle initial greeting through the entrypoint function
        print("Agent ready to collect user story information")
    
    async def on_exit(self):
        """Called when agent is about to be replaced or conversation ends"""
        print(f"Agent exiting conversation in phase: {self.conversation_phase.value}")

        # Fix: Add proper error handling for async operations
        try:
            # If we have a complete story, generate final output
            if self.story_data.is_complete() and self.conversation_phase != ConversationPhase.COMPLETE:
                print("Generating final output before exit...")
                await self.generate_final_output()
            elif not self.story_data.is_complete():
                print(f"Story incomplete at exit. Completion: {self.story_data.get_completion_percentage():.1f}%")
        except Exception as e:
            print(f"Error during agent exit: {e}")
            # Don't re-raise to avoid breaking the session cleanup
    
    def set_session(self, session: AgentSession):
        """Set the session reference for generating outputs"""
        # Fix: Store session reference for backward compatibility
        # In latest LiveKit patterns, session is managed externally
        self.session = session
    
    @function_tool
    async def collect_persona(
        self,
        context: RunContext,  # Required by function_tool framework
        persona: str,
        pain_points: str = "",
        goals: str = ""
    ):
        """
        Called when the user provides information about who the user story is for.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
            persona: The type of user or role (e.g., "store owner", "administrator")
            pain_points: Optional issues or challenges this user faces
            goals: Optional goals or objectives of this user
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        full_persona = persona
        if pain_points:
            full_persona += f" who struggles with {pain_points}"
        if goals:
            full_persona += f" and wants to {goals}"

        # Fix 2: Remove manual phase setting - let update_story_data handle it
        self.update_story_data(user_persona=full_persona)

        # Fix: According to latest LiveKit docs, function tools should return just the message
        return f"Great! I've captured that this story is for: {full_persona}. Now, what specific functionality does this user need?"
    
    @function_tool
    async def collect_functionality(
        self,
        context: RunContext,  # Required by function_tool framework
        functionality: str
    ):
        """
        Called when the user describes what functionality is needed.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
            functionality: The specific capability or action the user wants to perform
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        # Fix 2: Remove manual phase setting - let update_story_data handle it
        self.update_story_data(functionality=functionality)

        # Fix: According to latest LiveKit docs, function tools should return just the message
        return f"I understand that the user wants to: {functionality}. What business value or outcome will this provide?"
    
    @function_tool
    async def collect_business_value(
        self,
        context: RunContext,
        business_value: str
    ):
        """
        Called when the user explains the value or benefit of the functionality.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
            business_value: The benefit, outcome or value this functionality provides
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        # Fix 2: Remove manual phase setting - let update_story_data handle it
        self.update_story_data(business_value=business_value)

        # Check basic completeness
        core_story = f"As a {self.story_data.user_persona}, I want to {self.story_data.functionality} so that {business_value}."

        # Fix: According to latest LiveKit docs, function tools should return just the message
        return f"Excellent! We now have the core user story: '{core_story}' In what context or situation would this functionality be used?"
    
    @function_tool
    async def collect_context(
        self,
        context: RunContext,
        usage_context: str
    ):
        """
        Called when the user describes when or where the functionality would be used.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
            usage_context: When or where this functionality would be used
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        # Fix 2: Remove manual phase setting - let update_story_data handle it
        self.update_story_data(context=usage_context)

        # Fix: According to latest LiveKit docs, function tools should return just the message
        return f"I've added the context: '{usage_context}'. Now, let's define some acceptance criteria. What conditions must be met for this feature to be considered complete?"

    @function_tool
    async def collect_acceptance_criteria(
        self,
        context: RunContext,
        criteria: str
    ):
        """
        Called when the user provides acceptance criteria for the story.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
            criteria: Specific conditions that must be met for this story to be accepted
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        self.update_story_data(acceptance_criteria=criteria)

        # Fix: According to latest LiveKit docs, function tools should return just the message
        return f"Added acceptance criteria: '{criteria}'. Do you have additional criteria or any dependencies to add?"

    @function_tool
    async def collect_dependencies(
        self,
        context: RunContext,
        dependency: str
    ):
        """
        Called when the user adds a dependency for the story.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
            dependency: Other systems, features, or conditions this story depends on
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        self.update_story_data(dependencies=dependency)

        # Fix: According to latest LiveKit docs, function tools should return just the message
        return f"Added dependency: '{dependency}'. Anything else to add to this user story?"

    @function_tool
    async def finalize_user_story(
        self,
        context: RunContext
    ):
        """
        Called when the user indicates they are ready to finalize the user story.

        Args:
            context: RunContext provided by LiveKit framework (required but not used)
        """
        # Fix 3: Acknowledge context parameter to suppress warnings
        _ = context

        if not self.story_data.is_complete():
            missing_fields = []
            if not self.story_data.user_persona:
                missing_fields.append("user persona")
            if not self.story_data.functionality:
                missing_fields.append("functionality")
            if not self.story_data.business_value:
                missing_fields.append("business value")

            # Fix: According to latest LiveKit docs, function tools should return just the message
            return f"We still need information about: {', '.join(missing_fields)}. Let's fill those in first."

        self.conversation_phase = ConversationPhase.FINALIZING

        # Fix: Add proper error handling for async operations
        try:
            await self.generate_final_output()
            # Fix: According to latest LiveKit docs, function tools should return just the message
            return "User story has been finalized and saved!"
        except Exception as e:
            print(f"Error finalizing user story: {e}")
            return f"There was an error finalizing the user story: {e}. The story is still available in the console."
    
    def advance_conversation_phase(self):
        """Advance to the next conversation phase based on current data"""
        current_phase = self.conversation_phase
        
        if current_phase == ConversationPhase.GREETING and self.story_data.user_persona:
            self.conversation_phase = ConversationPhase.COLLECTING_FUNCTIONALITY
        elif current_phase == ConversationPhase.COLLECTING_FUNCTIONALITY and self.story_data.functionality:
            self.conversation_phase = ConversationPhase.COLLECTING_VALUE
        elif current_phase == ConversationPhase.COLLECTING_VALUE and self.story_data.business_value:
            self.conversation_phase = ConversationPhase.COLLECTING_CONTEXT
        elif current_phase == ConversationPhase.COLLECTING_CONTEXT:
            self.conversation_phase = ConversationPhase.COLLECTING_CRITERIA
        elif current_phase == ConversationPhase.COLLECTING_CRITERIA:
            self.conversation_phase = ConversationPhase.FINALIZING
        elif current_phase == ConversationPhase.FINALIZING:
            self.conversation_phase = ConversationPhase.COMPLETE
        
        if current_phase != self.conversation_phase:
            print(f"Conversation phase advanced: {current_phase.value} -> {self.conversation_phase.value}")
    
    async def generate_final_output(self):
        """Generate and output the final user story"""
        # Fix 1: Check session before using it
        if not self.session:
            print("❌ Error: Session not available for generating output")
            return

        if not self.story_data.is_complete():
            completion_pct = self.story_data.get_completion_percentage()
            await self.session.generate_reply(
                instructions=f"Let the user know we need more information to complete the user story. "
                           f"We're {completion_pct:.1f}% complete. Ask what's missing from the core requirements."
            )
            return

        # Mark as complete
        self.conversation_phase = ConversationPhase.COMPLETE

        final_story = self.story_data.to_formatted_story()

        # 1. Configuration Enhancement: Make output file configurable
        output_file = os.environ.get("USER_STORY_OUTPUT_FILE", "generated_user_story.txt")

        # Fix 4: Better backup file path handling
        import os.path
        if os.path.isabs(output_file):
            # For absolute paths, put backup in same directory
            dir_path, filename = os.path.split(output_file)
            backup_file = os.path.join(dir_path, f"backup_{filename}")
        else:
            # For relative paths, simple prefix
            backup_file = f"backup_{output_file}"

        # 2. Better Error Handling with fallback mechanism
        file_saved_successfully = False
        try:
            # Fix 5: Add encoding specification
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("# Generated User Story\n\n")
                f.write(final_story)
                f.write(f"\n\n# Generated on: {os.environ.get('USER', 'Unknown User')}")
                f.write(f"\n# Conversation turns: {self.conversation_turns}")
                f.write(f"\n# Final phase: {self.conversation_phase.value}")

            # 3. Output Validation: Verify file was written correctly
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                file_saved_successfully = True
                print(f"✅ Story saved successfully to: {output_file}")
            else:
                raise Exception("File exists but appears to be empty")

        except Exception as e:
            print(f"❌ Error saving story to {output_file}: {e}")

            # Fallback: Try to save to backup location
            try:
                # Fix 5: Add encoding specification
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write("# Generated User Story (Backup)\n\n")
                    f.write(final_story)
                    f.write(f"\n\n# Generated on: {os.environ.get('USER', 'Unknown User')}")
                    f.write(f"\n# Conversation turns: {self.conversation_turns}")
                    f.write(f"\n# Final phase: {self.conversation_phase.value}")
                    f.write(f"\n# Note: Saved to backup due to error with primary file")

                # Validate backup file
                if os.path.exists(backup_file) and os.path.getsize(backup_file) > 0:
                    file_saved_successfully = True
                    print(f"✅ Story saved to backup location: {backup_file}")
                else:
                    print(f"❌ Backup file creation failed")

            except Exception as backup_error:
                print(f"❌ Critical error: Could not save to backup location: {backup_error}")
                print("📝 Story will be displayed in console only")

        # Console output with enhanced formatting
        print(f"\n{'='*50}")
        print("FINAL USER STORY OUTPUT:")
        print('='*50)
        print(final_story)
        print('='*50)

        # Enhanced status reporting
        if file_saved_successfully:
            print(f"📁 File Status: ✅ Saved successfully")
        else:
            print(f"📁 File Status: ❌ Save failed - story available in console only")

        print(f"📊 Completion: {self.story_data.get_completion_percentage():.1f}%")
        print(f"🔄 Conversation turns: {self.conversation_turns}")

        # Generate final response to user
        await self.session.generate_reply(
            instructions=f"Present this final user story to the user and confirm it meets their needs. "
                        f"Congratulate them on creating a complete user story: {final_story}"
        )
    
    async def on_user_speech_committed(self, message: str):
        """Process incoming user message for information extraction"""
        print(f"Processing user message: {message[:50]}{'...' if len(message) > 50 else ''}")
        self.conversation_turns += 1
        
        # Simple keyword-based phase detection (would be enhanced with NLP in production)
        message_lower = message.lower()
        
        # Auto-detect function tool calls based on keywords
        if "finalize" in message_lower or "generate story" in message_lower or "complete" in message_lower:
            print("Detected finalization request")
            await self.finalize_user_story(RunContext())
        
    def update_story_data(self, **kwargs):
        """Update story data with new information and advance conversation phase"""
        updated = False
        for key, value in kwargs.items():
            if hasattr(self.story_data, key) and value:
                if key in ['acceptance_criteria', 'dependencies'] and isinstance(value, str):
                    # Convert string to list for criteria and dependencies
                    getattr(self.story_data, key).append(value)
                    updated = True
                else:
                    setattr(self.story_data, key, value)
                    updated = True
        
        if updated:
            self.advance_conversation_phase()
            print(f"Story data updated. Current completion: {self.story_data.get_completion_percentage():.1f}%")


async def entrypoint(ctx: agents.JobContext):
    # Fix: Follow latest LiveKit pattern - connect first
    await ctx.connect()

    # Get configuration files from environment variables or use defaults
    context_file = os.environ.get("AGENT_CONTEXT_FILE", "agent_context.txt")
    instructions_file = os.environ.get("AGENT_INSTRUCTIONS_FILE", "agent_instructions.txt")

    # Create a configurable agent instance
    agent = ConfigurableAgent(
        context_file_path=context_file,
        instructions_file_path=instructions_file
    )

    # Configure session components from environment variables or use defaults
    stt_model = os.environ.get("STT_MODEL", "nova-3")
    llm_model = os.environ.get("LLM_MODEL", "gpt-4o-mini")
    tts_model = os.environ.get("TTS_MODEL", "sonic-2")
    tts_voice = os.environ.get("TTS_VOICE", "f786b574-daa5-4673-aa0c-cbe3e8534c02")

    # Fix: Follow latest LiveKit pattern for session creation
    session = AgentSession(
        vad=silero.VAD.load(),
        stt=deepgram.STT(model=stt_model, language="multi"),
        llm=openai.LLM(model=llm_model),
        tts=cartesia.TTS(model=tts_model, voice=tts_voice),
        turn_detection=MultilingualModel(),
    )

    # Set session reference in agent for output generation (backward compatibility)
    agent.set_session(session)

    # Fix: Follow latest LiveKit pattern - start session with agent and room
    await session.start(
        agent=agent,
        room=ctx.room,
        room_input_options=RoomInputOptions(
            noise_cancellation=noise_cancellation.BVC(),
        ),
    )

    # Fix: Generate initial greeting using latest pattern
    initial_instructions = "Greet the user and offer your assistance."
    if agent.user_instructions:
        initial_instructions = agent.user_instructions

    await session.generate_reply(
        instructions=initial_instructions
    )

    print("Agent started and ready to collect user story information.")


if __name__ == "__main__":
    # Fix: Follow latest LiveKit CLI pattern
    from livekit.agents import cli
    cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))
