#!/usr/bin/env python3
"""
Demo script to show how the user story generation works
This simulates the conversation flow and generates a final user story output
"""

from configurable_agent import UserStoryData, ConfigurableAgent
import asyncio

def demo_story_generation():
    """Demonstrate the user story generation functionality"""
    
    # Create sample story data (this would normally be collected during conversation)
    story_data = UserStoryData(
        user_persona="small retail store owner managing 1-3 stores with basic tech skills",
        functionality="quickly reorder my best-selling products with one tap",
        business_value="I can maintain optimal inventory levels without spending time on complex ordering processes",
        context="During busy store hours when I notice popular items running low",
        acceptance_criteria=[
            "Given I'm logged into the app, When I tap 'Quick Reorder', Then I see my top 10 best-selling products",
            "Given I select products to reorder, When I confirm the order, Then it's processed within 2 hours",
            "Given the system is offline, When I try to reorder, Then my selections are saved and processed when connection returns"
        ],
        dependencies=[
            "Integration with inventory management system",
            "Historical sales data analysis",
            "Payment processing system"
        ]
    )
    
    # Generate and display the formatted story
    final_story = story_data.to_formatted_story()
    
    print("="*60)
    print("DEMO: GENERATED USER STORY OUTPUT")
    print("="*60)
    print(final_story)
    print("="*60)
    
    # Save to file
    output_file = "demo_user_story.txt"
    with open(output_file, 'w') as f:
        f.write("# Demo Generated User Story\n\n")
        f.write(final_story)
        f.write("\n\n# This is a demonstration of the final output format")
    
    print(f"Demo story saved to: {output_file}")
    
    # Show completion check
    print(f"\nStory completeness check: {'✅ Complete' if story_data.is_complete() else '❌ Incomplete'}")
    
    return story_data

def show_story_structure():
    """Show the structure of an empty user story for reference"""
    empty_story = UserStoryData()
    
    print("\n" + "="*60)
    print("USER STORY DATA STRUCTURE")
    print("="*60)
    print("Required fields for completion:")
    print("- user_persona: Who is the user?")
    print("- functionality: What do they want to do?") 
    print("- business_value: Why do they want it?")
    print("\nOptional fields:")
    print("- context: When/where is this used?")
    print("- acceptance_criteria: How do we know it works?")
    print("- dependencies: What else is needed?")
    print("="*60)

if __name__ == "__main__":
    print("User Story Generator Demo")
    print("This demonstrates the final output generation functionality\n")
    
    # Show the structure
    show_story_structure()
    
    # Run the demo
    demo_story_generation()
    
    print("\nTo use in the actual agent:")
    print("1. The agent collects information during conversation")
    print("2. Updates story_data using agent.update_story_data()")
    print("3. Calls agent.generate_final_output() when complete")
