#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced output functionality
Tests all three improvements: configuration, error handling, and validation
"""

import os
import tempfile
import asyncio
from configurable_agent import UserStoryData

class MockAgent:
    """Mock agent for testing output functionality"""
    def __init__(self):
        self.story_data = UserStoryData()
        self.conversation_phase = "COMPLETE"
        self.conversation_turns = 5
        self.session = MockSession()

    async def generate_final_output(self):
        """Copy of the enhanced generate_final_output method for testing"""
        if not self.story_data.is_complete():
            completion_pct = self.story_data.get_completion_percentage()
            await self.session.generate_reply(
                instructions=f"Let the user know we need more information to complete the user story. "
                           f"We're {completion_pct:.1f}% complete. Ask what's missing from the core requirements."
            )
            return

        final_story = self.story_data.to_formatted_story()

        # 1. Configuration Enhancement: Make output file configurable
        output_file = os.environ.get("USER_STORY_OUTPUT_FILE", "generated_user_story.txt")
        backup_file = f"backup_{output_file}"

        # 2. Better Error Handling with fallback mechanism
        file_saved_successfully = False
        try:
            with open(output_file, 'w') as f:
                f.write("# Generated User Story\n\n")
                f.write(final_story)
                f.write(f"\n\n# Generated on: {os.environ.get('USER', 'Unknown User')}")
                f.write(f"\n# Conversation turns: {self.conversation_turns}")
                f.write(f"\n# Final phase: {self.conversation_phase}")

            # 3. Output Validation: Verify file was written correctly
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                file_saved_successfully = True
                print(f"✅ Story saved successfully to: {output_file}")
            else:
                raise Exception("File exists but appears to be empty")

        except Exception as e:
            print(f"❌ Error saving story to {output_file}: {e}")

            # Fallback: Try to save to backup location
            try:
                with open(backup_file, 'w') as f:
                    f.write("# Generated User Story (Backup)\n\n")
                    f.write(final_story)
                    f.write(f"\n\n# Generated on: {os.environ.get('USER', 'Unknown User')}")
                    f.write(f"\n# Conversation turns: {self.conversation_turns}")
                    f.write(f"\n# Final phase: {self.conversation_phase}")
                    f.write(f"\n# Note: Saved to backup due to error with primary file")

                # Validate backup file
                if os.path.exists(backup_file) and os.path.getsize(backup_file) > 0:
                    file_saved_successfully = True
                    print(f"✅ Story saved to backup location: {backup_file}")
                else:
                    print(f"❌ Backup file creation failed")

            except Exception as backup_error:
                print(f"❌ Critical error: Could not save to backup location: {backup_error}")
                print("📝 Story will be displayed in console only")

        # Console output with enhanced formatting
        print(f"\n{'='*50}")
        print("FINAL USER STORY OUTPUT:")
        print('='*50)
        print(final_story)
        print('='*50)

        # Enhanced status reporting
        if file_saved_successfully:
            print(f"📁 File Status: ✅ Saved successfully")
        else:
            print(f"📁 File Status: ❌ Save failed - story available in console only")

        print(f"📊 Completion: {self.story_data.get_completion_percentage():.1f}%")
        print(f"🔄 Conversation turns: {self.conversation_turns}")

        # Generate final response to user
        await self.session.generate_reply(
            instructions=f"Present this final user story to the user and confirm it meets their needs. "
                        f"Congratulate them on creating a complete user story: {final_story}"
        )

class MockSession:
    """Mock session for testing"""
    async def generate_reply(self, instructions):
        print(f"🎤 Voice Response: {instructions[:100]}...")

async def test_enhanced_output():
    """Test the enhanced output functionality"""

    print("🧪 Testing Enhanced Output Functionality")
    print("="*60)

    # Create mock agent for testing
    agent = MockAgent()
    
    # Set up complete story data
    agent.story_data = UserStoryData(
        user_persona="test user with specific needs",
        functionality="perform a critical business function",
        business_value="achieve measurable business outcomes",
        context="in a specific operational context",
        acceptance_criteria=[
            "Given a condition, When an action occurs, Then a result happens",
            "Given another condition, When a different action occurs, Then another result happens"
        ],
        dependencies=[
            "External system integration",
            "Data validation service"
        ]
    )
    
    print("📋 Test 1: Default Configuration")
    print("-" * 40)
    await agent.generate_final_output()
    
    print("\n📋 Test 2: Custom Output File Configuration")
    print("-" * 40)
    os.environ["USER_STORY_OUTPUT_FILE"] = "custom_user_story.txt"
    await agent.generate_final_output()
    
    print("\n📋 Test 3: Error Handling - Invalid Path")
    print("-" * 40)
    os.environ["USER_STORY_OUTPUT_FILE"] = "/invalid/path/story.txt"
    await agent.generate_final_output()
    
    print("\n📋 Test 4: Error Handling - Permission Denied")
    print("-" * 40)
    # Create a read-only directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        readonly_file = os.path.join(temp_dir, "readonly_story.txt")
        # Create file and make directory read-only
        with open(readonly_file, 'w') as f:
            f.write("test")
        os.chmod(temp_dir, 0o444)  # Read-only
        
        os.environ["USER_STORY_OUTPUT_FILE"] = readonly_file
        await agent.generate_final_output()
        
        # Restore permissions for cleanup
        os.chmod(temp_dir, 0o755)
    
    # Clean up environment
    if "USER_STORY_OUTPUT_FILE" in os.environ:
        del os.environ["USER_STORY_OUTPUT_FILE"]
    
    print("\n📋 Test 5: Validation - File Existence and Size")
    print("-" * 40)
    
    # Test with default file
    await agent.generate_final_output()
    
    # Check if files exist and validate them
    test_files = ["generated_user_story.txt", "custom_user_story.txt"]
    for filename in test_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✅ {filename}: exists, size = {size} bytes")
            
            # Read and validate content
            with open(filename, 'r') as f:
                content = f.read()
                if "As a test user" in content:
                    print(f"✅ {filename}: content validation passed")
                else:
                    print(f"❌ {filename}: content validation failed")
        else:
            print(f"❌ {filename}: does not exist")
    
    print("\n🎉 Enhanced Output Testing Complete!")
    print("="*60)

def test_story_completeness():
    """Test story completeness validation"""
    print("\n📊 Testing Story Completeness Validation")
    print("-" * 40)
    
    # Test incomplete story
    incomplete_story = UserStoryData(
        user_persona="test user",
        functionality="",  # Missing
        business_value="achieve goals"
    )
    
    print(f"Incomplete story completion: {incomplete_story.get_completion_percentage():.1f}%")
    print(f"Is complete: {incomplete_story.is_complete()}")
    
    # Test complete story
    complete_story = UserStoryData(
        user_persona="test user",
        functionality="perform action",
        business_value="achieve goals"
    )
    
    print(f"Complete story completion: {complete_story.get_completion_percentage():.1f}%")
    print(f"Is complete: {complete_story.is_complete()}")

if __name__ == "__main__":
    print("Enhanced Output Functionality Test Suite")
    print("This demonstrates the three implemented improvements:")
    print("1. Configuration Enhancement")
    print("2. Better Error Handling") 
    print("3. Output Validation")
    print()
    
    # Run completeness test first
    test_story_completeness()
    
    # Run async output test
    asyncio.run(test_enhanced_output())
    
    print("\n📝 Summary of Improvements:")
    print("✅ 1. Configurable output file via USER_STORY_OUTPUT_FILE environment variable")
    print("✅ 2. Robust error handling with backup file fallback mechanism")
    print("✅ 3. File validation with existence and size checks")
    print("✅ 4. Enhanced status reporting with emojis and clear messaging")
    print("✅ 5. Graceful degradation when file operations fail")
