#!/usr/bin/env python3
"""
Test Step 1: Agent Lifecycle Events
This script tests the lifecycle events and conversation phase management
"""

import asyncio
from configurable_agent import ConfigurableAgent, ConversationPhase, UserStoryData

async def test_lifecycle_events():
    """Test agent lifecycle events and phase management"""
    
    print("="*60)
    print("TESTING STEP 1: AGENT LIFECYCLE EVENTS")
    print("="*60)
    
    # Create agent
    agent = ConfigurableAgent("agent_context.txt", "agent_instructions.txt")
    
    print(f"✅ Agent created with initial phase: {agent.conversation_phase.value}")
    print(f"✅ Story completion: {agent.story_data.get_completion_percentage():.1f}%")
    
    # Test conversation phase advancement
    print("\n--- Testing Conversation Phase Advancement ---")
    
    # Simulate collecting user persona
    agent.update_story_data(user_persona="small retail store owner")
    print(f"After persona: {agent.conversation_phase.value}")
    
    # Simulate collecting functionality
    agent.update_story_data(functionality="quickly reorder popular products")
    print(f"After functionality: {agent.conversation_phase.value}")
    
    # Simulate collecting business value
    agent.update_story_data(business_value="maintain optimal inventory without complex processes")
    print(f"After business value: {agent.conversation_phase.value}")
    
    # Test completion percentage
    completion = agent.story_data.get_completion_percentage()
    print(f"✅ Story completion: {completion:.1f}%")
    print(f"✅ Is complete: {agent.story_data.is_complete()}")
    
    # Test formatted output
    print("\n--- Testing Formatted Output ---")
    formatted_story = agent.story_data.to_formatted_story()
    print(formatted_story)
    
    print("\n--- Testing Phase Enum ---")
    all_phases = [phase.value for phase in ConversationPhase]
    print(f"✅ All conversation phases: {all_phases}")
    
    print("\n✅ STEP 1 VERIFICATION COMPLETE")
    print("✅ Agent lifecycle events implemented successfully")
    print("✅ Conversation phase management working")
    print("✅ Progress tracking functional")
    
    return agent

def test_data_structure():
    """Test the enhanced data structure"""
    print("\n--- Testing Enhanced Data Structure ---")
    
    # Test empty story
    empty_story = UserStoryData()
    print(f"Empty story completion: {empty_story.get_completion_percentage():.1f}%")
    print(f"Empty story is complete: {empty_story.is_complete()}")
    
    # Test partial story
    partial_story = UserStoryData(
        user_persona="test user",
        functionality="test function"
        # Missing business_value
    )
    print(f"Partial story completion: {partial_story.get_completion_percentage():.1f}%")
    print(f"Partial story is complete: {partial_story.is_complete()}")
    
    # Test complete story
    complete_story = UserStoryData(
        user_persona="store owner",
        functionality="reorder products",
        business_value="save time",
        context="during busy hours",
        acceptance_criteria=["Given logged in, When tap reorder, Then see products"],
        dependencies=["inventory system"]
    )
    print(f"Complete story completion: {complete_story.get_completion_percentage():.1f}%")
    print(f"Complete story is complete: {complete_story.is_complete()}")
    
    print("✅ Data structure enhancements working correctly")

if __name__ == "__main__":
    print("Step 1 Verification: Agent Lifecycle Events")
    print("This tests the enhanced agent with lifecycle events and phase management\n")
    
    # Test data structure
    test_data_structure()
    
    # Test lifecycle events (async)
    asyncio.run(test_lifecycle_events())
    
    print("\n🎉 Step 1 implementation verified successfully!")
    print("Next: Implement Function Tools (Step 2)")
