# LiveKit Configurable Agent

This project implements a configurable LiveKit AI agent that can load its context and instructions from external text files, making it versatile for multiple purposes.

## Features

- Load agent context (personality/role) from a configurable text file
- Load user interaction instructions from a separate text file
- Configure STT, LLM, and TTS models through environment variables
- Easy to adapt for different use cases without changing code

## Installation

1. Install the required dependencies:

```bash
pip install "livekit-agents[openai,silero,deepgram,cartesia,turn-detector]~=1.0"
```

2. Set up your environment variables in `.env` file:

```
# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-url
LIVEKIT_API_KEY=your-api-key
LIVEKIT_API_SECRET=your-api-secret

# OpenAI API Key (for LLM)
OPENAI_API_KEY=your-openai-api-key

# Optional: Configure agent components
STT_MODEL=nova-3
LLM_MODEL=gpt-4o-mini
TTS_MODEL=sonic-2
TTS_VOICE=f786b574-daa5-4673-aa0c-cbe3e8534c02

# Agent Configuration Files
AGENT_CONTEXT_FILE=agent_context.txt
AGENT_INSTRUCTIONS_FILE=agent_instructions.txt
```

## Configuration Files

### Context File (`agent_context.txt`)

This file defines the agent's role and personality. For example:

```
You are an AI assistant specialized in customer support for a software company. You have access to product documentation and can help users troubleshoot issues, explain features, and provide guidance on best practices. Always be professional, patient, and thorough in your responses.
```

### Instructions File (`agent_instructions.txt`)

This file contains instructions for how the agent should interact with users, what information to gather, and how to respond. For example:

```
Greet the user warmly and introduce yourself as a customer support assistant. Ask what type of assistance they need today. Be sure to gather the following information:
1. What product they're using
2. The version of the product
3. A brief description of their issue or question
4. Any error messages they may have encountered

Use this information to provide targeted help or route them to the appropriate department if necessary.
```

## Usage

Run the configurable agent:

```bash
python configurable_agent.py
```

## Creating Different Agents

To create different types of agents, simply create new context and instruction files and update the environment variables:

### Example: Technical Support Agent

1. Create `tech_support_context.txt` and `tech_support_instructions.txt`
2. Update environment variables:
   ```
   AGENT_CONTEXT_FILE=tech_support_context.txt
   AGENT_INSTRUCTIONS_FILE=tech_support_instructions.txt
   ```

### Example: Sales Agent

1. Create `sales_context.txt` and `sales_instructions.txt`
2. Update environment variables:
   ```
   AGENT_CONTEXT_FILE=sales_context.txt
   AGENT_INSTRUCTIONS_FILE=sales_instructions.txt
   ```

## Customization

You can customize the agent behavior by:

1. Modifying the context file to change the agent's persona
2. Updating the instructions file to change how it interacts with users
3. Adjusting environment variables to use different AI models
