#!/usr/bin/env python3
"""
Demo script showing the enhanced output features
This demonstrates how to use the three implemented improvements
"""

import os
from configurable_agent import UserStoryData

def demo_configuration_enhancement():
    """Demonstrate configurable output file"""
    print("🔧 DEMO 1: Configuration Enhancement")
    print("-" * 50)
    
    print("Default behavior:")
    print("- Output file: generated_user_story.txt (default)")
    
    print("\nWith environment variable:")
    print("- Set USER_STORY_OUTPUT_FILE=my_custom_story.txt")
    print("- Output file: my_custom_story.txt")
    
    print("\nUsage in code:")
    print("output_file = os.environ.get('USER_STORY_OUTPUT_FILE', 'generated_user_story.txt')")
    print()

def demo_error_handling():
    """Demonstrate enhanced error handling"""
    print("🛡️ DEMO 2: Better Error Handling")
    print("-" * 50)
    
    print("Enhanced error handling features:")
    print("✅ Graceful handling of file write errors")
    print("✅ Automatic backup file creation on failure")
    print("✅ Clear error messages with emojis")
    print("✅ Fallback to console-only output")
    print("✅ No crashes when file operations fail")
    
    print("\nError scenarios handled:")
    print("- Invalid file paths")
    print("- Permission denied errors")
    print("- Disk space issues")
    print("- Network drive failures")
    print()

def demo_output_validation():
    """Demonstrate output validation"""
    print("✅ DEMO 3: Output Validation")
    print("-" * 50)
    
    print("Validation features:")
    print("✅ File existence verification")
    print("✅ File size validation (non-empty)")
    print("✅ Success/failure status reporting")
    print("✅ Enhanced console feedback")
    
    print("\nValidation process:")
    print("1. Write file")
    print("2. Check if file exists")
    print("3. Verify file size > 0")
    print("4. Report status with clear messaging")
    print()

def demo_story_formatting():
    """Show the story formatting in action"""
    print("📝 DEMO 4: Story Formatting")
    print("-" * 50)
    
    # Create a sample story
    story = UserStoryData(
        user_persona="retail store manager with 5+ years experience",
        functionality="quickly generate weekly inventory reports",
        business_value="I can make data-driven restocking decisions without manual calculations",
        context="at the end of each week when planning next week's orders",
        acceptance_criteria=[
            "Given I'm logged into the system, When I click 'Generate Report', Then I see a summary of all product levels",
            "Given the report is generated, When I export it, Then I receive a PDF within 30 seconds"
        ],
        dependencies=[
            "Integration with POS system",
            "Access to historical sales data"
        ]
    )
    
    print("Sample formatted output:")
    print("=" * 60)
    print(story.to_formatted_story())
    print("=" * 60)
    print(f"Completion: {story.get_completion_percentage():.1f}%")
    print(f"Is complete: {'✅ Yes' if story.is_complete() else '❌ No'}")
    print()

def demo_usage_examples():
    """Show practical usage examples"""
    print("💡 DEMO 5: Usage Examples")
    print("-" * 50)
    
    print("Example 1: Custom output location")
    print("export USER_STORY_OUTPUT_FILE='/path/to/my/stories/story_001.txt'")
    print("python configurable_agent.py")
    print()
    
    print("Example 2: Project-specific naming")
    print("export USER_STORY_OUTPUT_FILE='project_alpha_user_story.txt'")
    print("python configurable_agent.py")
    print()
    
    print("Example 3: Timestamped files")
    print("export USER_STORY_OUTPUT_FILE=\"story_$(date +%Y%m%d_%H%M%S).txt\"")
    print("python configurable_agent.py")
    print()

if __name__ == "__main__":
    print("🎯 Enhanced Output Features Demo")
    print("=" * 60)
    print("This demo shows the three implemented improvements:")
    print("1. Configuration Enhancement")
    print("2. Better Error Handling")
    print("3. Output Validation")
    print()
    
    demo_configuration_enhancement()
    demo_error_handling()
    demo_output_validation()
    demo_story_formatting()
    demo_usage_examples()
    
    print("🎉 Demo Complete!")
    print("=" * 60)
    print("Key Benefits:")
    print("✅ More reliable file operations")
    print("✅ Better user experience with clear feedback")
    print("✅ Configurable output locations")
    print("✅ Graceful error handling")
    print("✅ No data loss even when file operations fail")
    print()
    print("To test these features, run: python test_enhanced_output.py")
